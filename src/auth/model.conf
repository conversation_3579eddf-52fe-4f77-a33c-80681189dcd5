# Casbin RBAC 模型配置文件
# 支持多租户的基于角色的访问控制 (RBAC with Tenants)

[request_definition]
# 请求格式: subject(用户), domain(租户), object(资源), action(操作)
r = sub, dom, obj, act

[policy_definition]
# 策略格式: subject(角色), domain(租户), object(资源), action(操作)
p = sub, dom, obj, act

[role_definition]
# 角色继承/用户角色映射: user, role, domain(租户)
# 含义: 用户(user)在指定的租户(domain)内，被授予某个角色(role)
g = _, _, _

[policy_effect]
# 效果: 只要有一条策略允许，就允许
e = some(where (p.eft == allow))

[matchers]
# 匹配器:
# 1. 检查用户(r.sub)在租户(r.dom)下是否拥有角色(p.sub) -> g(r.sub, p.sub, r.dom)
# 2. 检查请求的租户是否与策略的租户匹配 -> r.dom == p.dom
# 3. 检查请求的资源是否匹配策略的资源模式 -> keyMatch2(r.obj, p.obj)
# 4. 检查请求的操作是否匹配策略的操作 -> r.act == p.act
m = g(r.sub, p.sub, r.dom) && r.dom == p.dom && keyMatch2(r.obj, p.obj) && r.act == p.act

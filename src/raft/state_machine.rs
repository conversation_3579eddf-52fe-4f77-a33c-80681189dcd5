//! 独立的Raft状态机实现
//!
//! 这个模块实现了openraft 0.9需要的RaftStateMachine trait，
//! 与日志存储完全分离，专注于状态变更。

use crate::raft::store::Store;
use crate::raft::types::*;
use openraft::{
    storage::{Snapshot, SnapshotMeta},
    Entry, EntryPayload, LogId, StorageError, StoredMembership,
};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, error, info};

/// 独立的Raft状态机实现
///
/// 负责将Raft日志条目应用到应用状态，使用apply_state_change避免循环依赖
#[derive(Debug)]
pub struct ConfluxStateMachine {
    /// 底层存储实例
    store: Arc<Store>,
    /// 最后应用的日志ID
    last_applied_log: Option<LogId<NodeId>>,
    /// 最后的成员关系
    last_membership: StoredMembership<NodeId, Node>,
}

impl ConfluxStateMachine {
    /// 创建新的状态机实例
    pub fn new(store: Arc<Store>) -> Self {
        Self {
            store,
            last_applied_log: None,
            last_membership: StoredMembership::default(),
        }
    }

    /// 获取最后应用的日志ID
    pub fn last_applied_log(&self) -> Option<LogId<NodeId>> {
        self.last_applied_log
    }

    /// 获取最后的成员关系
    pub fn last_membership(&self) -> &StoredMembership<NodeId, Node> {
        &self.last_membership
    }

    /// 应用业务命令到状态
    ///
    /// 使用apply_state_change而不是apply_command避免循环依赖
    async fn apply_business_command(
        &mut self,
        command: &RaftCommand,
    ) -> Result<ClientWriteResponse, StorageError<NodeId>> {
        debug!("Applying business command: {:?}", command);

        match self.store.apply_state_change(command).await {
            Ok(response) => {
                info!("Business command applied successfully");
                Ok(response)
            }
            Err(e) => {
                error!("Failed to apply business command: {}", e);
                Err(StorageError::IO {
                    source: openraft::StorageIOError::new(
                        openraft::ErrorSubject::StateMachine,
                        openraft::ErrorVerb::Write,
                        openraft::AnyError::error(format!("Business command failed: {}", e)),
                    ),
                })
            }
        }
    }

    /// 应用成员关系变更
    async fn apply_membership_change(
        &mut self,
        log_id: LogId<NodeId>,
        membership: openraft::Membership<NodeId, Node>,
    ) -> Result<(), StorageError<NodeId>> {
        debug!(
            "Applying membership change at log {}: {:?}",
            log_id, membership
        );

        self.last_membership = StoredMembership::new(Some(log_id), membership);

        info!("Membership updated successfully");
        Ok(())
    }

    /// 应用单个日志条目
    async fn apply_entry(
        &mut self,
        entry: &Entry<TypeConfig>,
    ) -> Result<ClientWriteResponse, StorageError<NodeId>> {
        // 更新最后应用的日志ID
        self.last_applied_log = Some(entry.log_id);

        match &entry.payload {
            EntryPayload::Blank => {
                debug!("Applied blank entry at log {}", entry.log_id);
                Ok(ClientWriteResponse {
                    config_id: None,
                    success: true,
                    message: "Blank entry applied".to_string(),
                    data: None,
                })
            }
            EntryPayload::Normal(ref data) => {
                debug!("Applying normal entry at log {}: {:?}", entry.log_id, data);
                self.apply_business_command(&data.command).await
            }
            EntryPayload::Membership(ref membership) => {
                debug!(
                    "Applying membership entry at log {}: {:?}",
                    entry.log_id, membership
                );
                self.apply_membership_change(entry.log_id, membership.clone())
                    .await?;
                Ok(ClientWriteResponse {
                    config_id: None,
                    success: true,
                    message: "Membership updated".to_string(),
                    data: None,
                })
            }
        }
    }

    /// 批量应用日志条目
    pub async fn apply_entries(
        &mut self,
        entries: &[Entry<TypeConfig>],
    ) -> Result<Vec<ClientWriteResponse>, StorageError<NodeId>> {
        let mut responses = Vec::with_capacity(entries.len());

        for entry in entries {
            match self.apply_entry(entry).await {
                Ok(response) => responses.push(response),
                Err(e) => {
                    error!("Failed to apply entry at log {}: {}", entry.log_id, e);
                    responses.push(ClientWriteResponse {
                        config_id: None,
                        success: false,
                        message: format!("Failed to apply entry: {}", e),
                        data: None,
                    });
                }
            }
        }

        info!("Applied {} entries successfully", entries.len());
        Ok(responses)
    }

    /// 获取当前状态机状态（用于快照）
    pub async fn get_state(&self) -> Result<Vec<u8>, StorageError<NodeId>> {
        debug!("Getting state machine state for snapshot");

        let state = StateMachineSnapshot {
            last_applied_log: self.last_applied_log,
            last_membership: self.last_membership.clone(),
        };

        serde_json::to_vec(&state).map_err(|e| StorageError::IO {
            source: openraft::StorageIOError::new(
                openraft::ErrorSubject::Snapshot(None),
                openraft::ErrorVerb::Write,
                openraft::AnyError::error(format!("Failed to serialize state: {}", e)),
            ),
        })
    }

    /// 从快照恢复状态机状态
    pub async fn restore_from_snapshot(&mut self, data: &[u8]) -> Result<(), StorageError<NodeId>> {
        debug!("Restoring state machine from snapshot");

        let state: StateMachineSnapshot =
            serde_json::from_slice(data).map_err(|e| StorageError::IO {
                source: openraft::StorageIOError::new(
                    openraft::ErrorSubject::Snapshot(None),
                    openraft::ErrorVerb::Read,
                    openraft::AnyError::error(format!("Failed to deserialize state: {}", e)),
                ),
            })?;

        self.last_applied_log = state.last_applied_log;
        self.last_membership = state.last_membership;

        info!("State machine restored from snapshot successfully");
        Ok(())
    }
}

/// 状态机快照数据结构
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
struct StateMachineSnapshot {
    last_applied_log: Option<LogId<NodeId>>,
    last_membership: StoredMembership<NodeId, Node>,
}

/// 状态机包装器，用于与openraft集成
#[derive(Debug)]
pub struct ConfluxStateMachineWrapper {
    inner: Arc<RwLock<ConfluxStateMachine>>,
}

impl ConfluxStateMachineWrapper {
    /// 创建新的状态机包装器
    pub fn new(store: Arc<Store>) -> Self {
        Self {
            inner: Arc::new(RwLock::new(ConfluxStateMachine::new(store))),
        }
    }

    /// 获取状态机状态信息
    pub async fn get_state_info(&self) -> (Option<LogId<NodeId>>, StoredMembership<NodeId, Node>) {
        let sm = self.inner.read().await;
        (sm.last_applied_log, sm.last_membership.clone())
    }

    /// 获取内部状态机的访问权限（用于 RaftStateMachine 实现）
    pub(crate) fn inner(&self) -> &Arc<RwLock<ConfluxStateMachine>> {
        &self.inner
    }
}

impl Clone for ConfluxStateMachineWrapper {
    fn clone(&self) -> Self {
        Self {
            inner: self.inner.clone(),
        }
    }
}

// RaftStateMachine trait 现在在 raft_storage_v2.rs 中实现
// 这避免了重复实现的问题

/// 快照构建器实现
#[derive(Debug)]
pub struct ConfluxSnapshotBuilder {
    state_machine: Arc<RwLock<ConfluxStateMachine>>,
}

impl ConfluxSnapshotBuilder {
    pub fn new(state_machine: Arc<RwLock<ConfluxStateMachine>>) -> Self {
        Self { state_machine }
    }
}

impl openraft::storage::RaftSnapshotBuilder<TypeConfig> for ConfluxSnapshotBuilder {
    async fn build_snapshot(&mut self) -> Result<Snapshot<TypeConfig>, StorageError<NodeId>> {
        debug!("Building snapshot");

        let (data, last_applied, membership) = {
            let sm = self.state_machine.read().await;
            let data = sm.get_state().await?;
            (data, sm.last_applied_log, sm.last_membership.clone())
        };

        let meta = SnapshotMeta {
            last_log_id: last_applied,
            last_membership: membership,
            snapshot_id: format!("snapshot-{}", chrono::Utc::now().timestamp()),
        };

        Ok(Snapshot {
            meta,
            snapshot: Box::new(std::io::Cursor::new(data)),
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::raft::store::Store;
    use openraft::{CommittedLeaderId, Entry, EntryPayload, LogId};
    use tempfile::TempDir;

    async fn create_test_state_machine() -> (ConfluxStateMachine, TempDir) {
        let temp_dir = tempfile::tempdir().unwrap();
        let (store, _event_receiver) = Store::new(temp_dir.path().to_str().unwrap()).await.unwrap();
        let store = Arc::new(store);
        let state_machine = ConfluxStateMachine::new(store);
        (state_machine, temp_dir)
    }

    #[tokio::test]
    async fn test_state_machine_creation() {
        let (state_machine, _temp_dir) = create_test_state_machine().await;
        assert!(state_machine.last_applied_log().is_none());
    }

    #[tokio::test]
    async fn test_blank_entry_application() {
        let (mut state_machine, _temp_dir) = create_test_state_machine().await;

        let entry = Entry {
            log_id: LogId::new(CommittedLeaderId::new(1, 1), 1),
            payload: EntryPayload::Blank,
        };

        let result = state_machine.apply_entry(&entry).await.unwrap();
        assert!(result.success);
        assert_eq!(state_machine.last_applied_log(), Some(entry.log_id));
    }

    #[tokio::test]
    async fn test_wrapper_integration() {
        // 暂时跳过这个测试，专注于核心功能
        // TODO: 实现完整的测试
    }
}

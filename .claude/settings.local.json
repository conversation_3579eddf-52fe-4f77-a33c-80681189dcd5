{"permissions": {"allow": ["mcp__zen__analyze", "mcp__github_com_modelcontextprotocol_servers_tree_main_src_filesystem__directory_tree", "Bash(find:*)", "mcp__github_com_modelcontextprotocol_servers_tree_main_src_sequentialthinking__sequentialthinking", "Bash(cargo check:*)", "Bash(cargo doc:*)", "Bash(cargo test:*)", "Bash(cargo run:*)", "mcp__github_com_modelcontextprotocol_servers_tree_main_src_filesystem__write_file", "Bash(cargo:*)", "<PERSON><PERSON>(rust-analyzer:*)", "Bash(rustc:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["zen"]}
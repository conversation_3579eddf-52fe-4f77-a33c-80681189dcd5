-- 创建Casbin规则表
-- 这个表用于存储Casbin的策略规则和角色分配

CREATE TABLE IF NOT EXISTS casbin_rule (
    id SERIAL PRIMARY KEY,
    ptype VARCHAR(100) NOT NULL,  -- 策略类型: 'p' for policy, 'g' for grouping
    v0 VARCHAR(100) NOT NULL,     -- 第一个值: 角色/用户
    v1 VARCHAR(100) NOT NULL,     -- 第二个值: 租户/角色
    v2 VARCHAR(100) NOT NULL,     -- 第三个值: 资源/租户
    v3 VARCHAR(100) NOT NULL,     -- 第四个值: 操作/空
    v4 VARCHAR(100) NOT NULL DEFAULT '',  -- 第五个值: 通常为空
    v5 VARCHAR(100) NOT NULL DEFAULT '',  -- 第六个值: 通常为空
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_casbin_rule_ptype ON casbin_rule(ptype);
CREATE INDEX IF NOT EXISTS idx_casbin_rule_v0 ON casbin_rule(v0);
CREATE INDEX IF NOT EXISTS idx_casbin_rule_v1 ON casbin_rule(v1);
CREATE INDEX IF NOT EXISTS idx_casbin_rule_v2 ON casbin_rule(v2);

-- 创建复合索引用于常见查询模式
CREATE INDEX IF NOT EXISTS idx_casbin_rule_ptype_v0_v1 ON casbin_rule(ptype, v0, v1);
CREATE INDEX IF NOT EXISTS idx_casbin_rule_ptype_v0_v1_v2 ON casbin_rule(ptype, v0, v1, v2);

-- 插入一些初始的测试数据
-- 创建超级管理员角色，拥有所有权限
INSERT INTO casbin_rule (ptype, v0, v1, v2, v3) VALUES 
('p', 'super_admin', '*', '/*', '*'),
('p', 'tenant_admin', 'tenant1', '/tenants/tenant1/*', '*'),
('p', 'developer', 'tenant1', '/tenants/tenant1/apps/*', 'read'),
('p', 'developer', 'tenant1', '/tenants/tenant1/apps/*', 'write'),
('p', 'viewer', 'tenant1', '/tenants/tenant1/apps/*', 'read');

-- 分配一些测试用户角色
INSERT INTO casbin_rule (ptype, v0, v1, v2) VALUES 
('g', 'admin', 'super_admin', '*'),
('g', 'alice', 'tenant_admin', 'tenant1'),
('g', 'bob', 'developer', 'tenant1'),
('g', 'charlie', 'viewer', 'tenant1');

-- 创建更新时间戳的触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_casbin_rule_updated_at 
    BEFORE UPDATE ON casbin_rule 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
